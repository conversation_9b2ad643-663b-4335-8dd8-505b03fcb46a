<script setup lang="ts">
import { CardCommon } from '@src-console/components'
import { SettingItem, SettingDivision } from '@xiaou66/u-web-ui';
function handleBindWechat() {
  console.log('handleBindWechat Click')
}
function handleBindEmail() {
  console.log('handleBindEmail Click')
}
function handleAccountId() {
  console.log('handleAccountId Click')
}
</script>

<template>
  <div class="grid gap-2">
    <CardCommon title="个人信息">
      <div>
        <SettingItem name="账号ID" @click="handleAccountId">
          001
        </SettingItem>
        <SettingItem name="微信"
                     desc="绑定后可以使用微信登录"
                     @click="handleBindWechat">
          <!--        <t-button theme="success"-->
          <!--                  @click.stop="handleBindWechat">-->
          <!--          <template #icon>-->
          <!--            <t-icon class="i-u-wechat"></t-icon>-->
          <!--          </template>-->
          <!--          绑定微信-->
          <!--        </t-button>-->
          <div class="flex gap-2 items-center">
            <div>xiaou</div>
            <t-avatar
              shape="round"
              image="https://thirdwx.qlogo.cn/mmopen/vi_32/EktcAfFAwo5Tdg5BRZ1jMlJXIJUagymAILdKoWiadlRpjS43e3bdVnqpPicBIgweA0RA4otKC0ReY8LKLfhy6icvg/132">
            </t-avatar>
          </div>
        </SettingItem>
        <SettingItem name="邮箱" @click="handleBindEmail">
          <t-button variant="outline" @click.stop="handleBindEmail">绑定邮箱</t-button>
          <!--        <div>
                    <EMAIL>
                  </div>-->
        </SettingItem>
        <SettingDivision />
      </div>
    </CardCommon>
    <CardCommon title="账号">
      <SettingItem name="积分">
        <div>10</div>
      </SettingItem>
      <SettingItem name="账号注册时间">
        <div>2025-12-31</div>
      </SettingItem>
    </CardCommon>
  </div>
</template>

<style scoped>

</style>
