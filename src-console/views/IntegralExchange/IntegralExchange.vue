<script setup lang="ts">
import { CardGroup } from '@src-console/components'
import { SettingItem } from '@xiaou66/u-web-ui';

</script>

<template>
  <div class="grid gap-2">
    <CardGroup title="服务">
      <SettingItem name="解决常见问题" desc="解决非产品问题和常规使用问题">
        100 积分起
      </SettingItem>
      <SettingItem name="协助代码问题"
                   desc="兑换前联系开发者确认后兑换">
        200 积分起
      </SettingItem>
      <SettingItem name="协助开源项目搭建" desc="兑换前联系开发者确认后兑换">
        500 积分起
      </SettingItem>
    </CardGroup>
    <CardGroup title="虚拟物品">
      <SettingItem name="截图工具永久会员" desc="兑换前联系开发者确认后兑换">
        300 积分
      </SettingItem>
    </CardGroup>
    <CardGroup title="实物物品">
      <t-empty title="暂无">
      </t-empty>
    </CardGroup>
  </div>
</template>

<style scoped lang="less">

</style>
