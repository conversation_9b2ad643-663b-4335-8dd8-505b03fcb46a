import { fileURLToPath, resolve, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import vueDevTools from 'vite-plugin-vue-devtools'
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { TDesignResolver } from 'unplugin-vue-components/resolvers';
import UnoCSS from 'unocss/vite'
import { codeInspectorPlugin } from 'code-inspector-plugin'
// https://vite.dev/config/
export default defineConfig({
  server: {
    open: true,
    port: 5173,
    host: true,
  },
  build: {
    rollupOptions: {
      input: {
        main: resolve(fileURLToPath(new URL('.', import.meta.url)), 'index.html'),
        login: resolve(fileURLToPath(new URL('.', import.meta.url)), 'src-login/login.html'),
        console: resolve(fileURLToPath(new URL('.', import.meta.url)), 'src-console/console.html'),
      },
    }
  },
  plugins: [
    vue(),
    vueJsx(),
    vueDevTools(),
    UnoCSS(),
    codeInspectorPlugin({
      bundler: 'vite',
      editor: 'webstorm',
    }),
    AutoImport({
      resolvers: [TDesignResolver({
        library: 'vue-next'
      }),
      TDesignResolver({
        library: 'mobile-vue',
      })],
    }),
    Components({
      resolvers: [TDesignResolver({
        library: 'vue-next',
      }), TDesignResolver({
        library: 'mobile-vue',
      })],
    }),
    // 自定义插件处理路由重写
    {
      name: 'multi-page-router',
      configureServer(server) {
        server.middlewares.use((req, res, next) => {
          if (req.url?.startsWith('/login')) {
            req.url = '/src-login/login.html'
          } else if (req.url?.startsWith('/console')) {
            req.url = '/src-console/console.html'
          }
          next()
        })
      }
    }
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      "@src-login": fileURLToPath(new URL('./src-login', import.meta.url)),
      "@src-console": fileURLToPath(new URL('./src-console', import.meta.url)),
      "@src-common": fileURLToPath(new URL('./src-common', import.meta.url)),
    },
  },
})
